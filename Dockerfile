# --- Build Stage ---
FROM node:18-alpine AS builder

# Create a non-root user
RUN addgroup -S appgroup
RUN adduser -S -u 1100 -G appgroup appuser

# Set the working directory
WORKDIR /app

# Copy package files first for better caching
COPY src/package*.json ./src/

# Install dependencies
RUN cd src && npm ci --only=production

# --- Runtime Stage ---
FROM node:18-alpine

# Create a non-root user
RUN addgroup -S appgroup
RUN adduser -S -u 1100 -G appgroup appuser

# Set the working directory
WORKDIR /app

# Ensure ownership for potential runtime files
RUN mkdir -p /app/configs /app/static /app/logs
RUN chown -R appuser:appgroup /app

# Copy the entire src directory from builder stage
COPY --from=builder --chown=appuser:appgroup /app/src ./src

# Copy static files if your server serves them
COPY src/static/ /app/static/

# Copy config files if your server reads them from a 'configs' directory
COPY configs/ /app/configs/

# Copy rmconfig directory from parent directory
COPY ../rmconfig/ /app/rmconfig/

# Make start.sh executable
RUN chmod +x /app/rmconfig/start.sh

# Switch to the non-root user
USER appuser

# Expose the port your server listens on
EXPOSE 8099

# Command to run your server when the container starts
# Changed to use start.sh -k as requested
CMD ["/bin/sh", "-c", "cd /app/rmconfig && ./start.sh -k"]
