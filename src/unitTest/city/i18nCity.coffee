should = require('should')
helpers = require('../00_common/helpers')
I18nCityCol = null
{reloadCity, shouldTransCity} = require('../../built/lib/i18n')
{getFormatCityName} = require('../../built/libapp/propertiesTranslate')
sinon = require('sinon')

# i18nCity 测试用例
# 包含 reloadCity 和 shouldTransCity 的相关测试

describe 'i18nCity test',->
  before (done) ->
    @timeout(300000)
    I18nCityCol = helpers.COLLECTION 'chome','i18nCity'
    dbs = [
      { db: 'chome', table: 'user' },
      { db: 'chome', table: 'login' },
      { db: 'chome', table: 'i18nCity' },
    ]
    helpers.cleanDBs dbs, () ->
      helpers.checkAndsetUpFixture {folder:__dirname}, () ->
        setTimeout(done, 2500)
    return

  describe 'reloadCity',->
    # 测试 coll 为 null 时应返回错误
    it 'should return error if coll is null', ->
      err = null
      try
        await reloadCity {coll: null}
      catch e
        err = e
      should.exist(err)

    # 测试未传 key 时应加载所有城市数据
    it 'should load all city data when key is not provided', ->
      err = null
      try
        await reloadCity {coll: I18nCityCol}
      catch e
        err = e
      should.not.exist(err)
      should(shouldTransCity('Oakville')).be.true()
      should(shouldTransCity('West Lincoln')).be.false()
      should(shouldTransCity('Halton Hills')).be.false()
      should(shouldTransCity('Toronto')).be.true()
      should(shouldTransCity('Ottawa')).be.true()
      should(shouldTransCity('Borden')).be.true()

    # 测试带有空白字符的城市名称
    it 'should handle city names with whitespace', ->
      should(shouldTransCity(' Toronto ')).be.true()
      should(shouldTransCity('  Oakville  ')).be.true()
      should(shouldTransCity('\tHalton Hills\t')).be.false()
      should(shouldTransCity('\nWest Lincoln\n')).be.false()
      
    # 测试传 key 时只更新一个城市
    it 'should update only one city when key is provided', ->
      err = null
      try
        await reloadCity {coll: I18nCityCol}
        # 修改一条数据，使用 await 保证顺序
        await I18nCityCol.updateOne {_id: 'on_oakville'}, {$set: {shouldTrans: false}}
        await reloadCity {coll: I18nCityCol, key: 'oakville'}
      catch e
        err = e
      should.not.exist(err)
      should(shouldTransCity('Oakville')).be.false()
    
    # 测试传 key 时只更新一个城市, records存在多条
    it 'should update only one city when key is provided', ->
      err = null
      try
        await reloadCity {coll: I18nCityCol}
        # 修改一条数据，使用 await 保证顺序
        await I18nCityCol.updateOne {_id: 'sk_borden'}, {$set: {shouldTrans: false}}
        await reloadCity {coll: I18nCityCol, key: 'Borden'}
      catch e
        err = e
      should.not.exist(err)
      should(shouldTransCity('Borden')).be.false()

  describe 'shouldTransCity',->
    # 测试已存在城市的 shouldTrans 值
    it 'should return correct shouldTrans for existing city', ->
      should(shouldTransCity('Toronto')).be.true()
      should(shouldTransCity('Halton Hills')).be.false()

    # 测试不存在的城市应返回 false 并警告
    it 'should return false and warn for non-existing city', ->
      spy = sinon.spy(console, 'warn')
      should(shouldTransCity('NonExistCity')).be.false()
      spy.called.should.be.true()
      spy.restore()

    # 测试空值和无效输入
    it 'should handle null and invalid inputs', ->
      should(shouldTransCity(null)).be.false()
      should(shouldTransCity(undefined)).be.false()
      should(shouldTransCity('')).be.false()
      should(shouldTransCity('   ')).be.false()

  describe 'getFormatCityName',->
    # 测试单个城市名称的处理
    it 'should handle single city name', ->
      prop = {origCity: 'Toronto', city_en: 'Toronto', area: 'Toronto'}
      result = getFormatCityName(prop, 'en')
      should(result).equal('Toronto')

    # 测试需要翻译的城市名称
    it 'should translate city name when shouldTransCity is true', ->
      prop = {origCity: ' Toronto ', city_en: 'Toronto', area: 'Toronto'}
      result = getFormatCityName(prop, 'zh')
      should(result).not.equal('Toronto')

    # 测试不需要翻译的城市名称
    it 'should not translate city name when shouldTransCity is false', ->
      prop = {origCity: 'Halton Hills', city_en: 'Halton Hills', area: 'Halton Hills'}
      result = getFormatCityName(prop, 'zh')
      should(result).equal('Halton Hills')

    # 测试多个城市名称的处理
    it 'should handle multiple city names', ->
      prop = {origCity: 'Toronto', city_en: 'Toronto', area: 'GTA'}
      result = getFormatCityName(prop, 'en').split(',')
      result = result.map((city)-> city.trim())
      result.includes('Toronto').should.be.true()
      result.includes('GTA').should.be.true() # 包含GTA,但是由于join(', '),所以需要trim()处理

    # 测试包含关系的城市名称
    it 'should handle city names with inclusion relationship', ->
      prop = {origCity: 'Toronto', city_en: 'Greater Toronto Area', area: 'GTA'}
      result = getFormatCityName(prop, 'en').split(',')
      result.includes('Toronto').should.be.false()
      result.includes('Greater Toronto Area').should.be.true()

    # 测试空值和无效输入
    it 'should handle null and invalid inputs', ->
      prop = {origCity: null, city_en: null, area: null}
      result = getFormatCityName(prop, 'en')
      should(result).equal('')

      prop = {origCity: '', city_en: '', area: ''}
      result = getFormatCityName(prop, 'en')
      should(result).equal('')