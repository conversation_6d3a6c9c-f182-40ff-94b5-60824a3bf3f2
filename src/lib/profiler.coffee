###
Profiler:
pf = require 'profiler'
pf.start options
pf.stop()

options:
  file: absolute file names
  freq: milliseconds
  profile: strings separated by ','.  memory,load,request,response
  console: boolean - output to console instead of file
  formatName: string - 'json' for JSON format, otherwise default format
###
Path = require 'path'
OS = require 'os'
fs = require 'fs'
helpers = require './helpers'
# mongodb = require './mongo_db'
mongodb = require './mongo4'
elasticsearchLib = require './elasticsearch_async'
require './date'
logger_profile = require('./log').profile
b2m = helpers.numberReduce
fix = helpers.fixlengthString
mate = null

stream = null
tmpdir = OS.tmpdir or OS.tmpDir
options =
  file: Path.join tmpdir(), "node-#{process.pid}.prf-log"
  freq: 60*1000
  profile: true
  console: false
  formatName: null

# Output mode tracking
isConsoleOutput = false
isJsonFormat = false
headerKeys = []

DEFAULT_DATETIME_FORMAT = "yyyy-mm-dd'T'HH:MM:ss.l"
start_time = null
results = []
add = (v)-> results.push v
_all_profilers =
  timer:
    title: ->
      add 'YYYY-MM-DDTHH:MM:SS.mssZ'
      add 'Profile-Time'
      start_time = Date.now()
    profile: ->
      now = Date.now()
      add (new Date(now)).format(DEFAULT_DATETIME_FORMAT)
      p = now - start_time
      ms = p % 1000
      p = Math.floor(p / 1000)
      s = p % 60
      p = Math.floor(p / 60)
      m = p % 60
      p = Math.floor(p / 60)
      h = p % 24
      p = Math.floor(p / 24)
      d = p
      add "#{d}d#{h}:#{m}:#{s}"
  memory:
    title: ->
      add 'RssMemory'
      add 'heapUsed'
      add 'heapTotal'
      add 'freeMemory'
      add 'totalMemory'
    profile: ->
      try
        m = process.memoryUsage()
        add b2m m.rss
        add b2m m.heapUsed
        add b2m m.heapTotal
      catch e
        console.error e
        add 'err'
        add 'err'
        add 'err'
      add b2m OS.freemem()
      add b2m OS.totalmem()
  load:
    title: ->
      add 'load-1'
      add 'load-5'
      add 'load15'
    profile: ->
      a = OS.loadavg()
      for l in a
        add helpers.round l,3
  request:
    title: ->
      add 'request'
      add 'procesd'
    profile: ->
      a = mate?.profile() or [null,null]
      add a[0]
      add a[1]
  response:
    title: ->
      add 'reqLgd'
      add 'resp-avg'
      add '404'
      add 'TmOvr'
    profile: ->
      p = logger_profile()
      add p[0]
      if p[0] isnt 0
        add helpers.round (p[1] / p[0]),2
      else
        add '-'
      add p[2]
      add p[3]
  database:
    title: ->
      add 'dbReq'
      add 'dbMax'
      add 'dbAvgMs'
      add 'dbErr'
    profile: ->
      p = mongodb.profile()
      add p.n
      add p.tsMax
      add Math.round(p.ts / (p.n or 1) * 100) / 100
      add p.en
  elasticsearch:
    title: ->
      add 'esReq'
      add 'esMax'
      add 'esAvgMs'
      add 'esErr'
    profile: ->
      p = elasticsearchLib.ESProfile()
      add p.n
      add p.tsMax
      add Math.round(p.ts / (p.n or 1) * 100) / 100
      add p.en
title = null
titleL = []
profilers = []
counter = 0

# Helper function to write output
writeOutput = (content, cb)->
  if isConsoleOutput
    console.log content
    cb() if cb
  else if stream?
    stream.write content, cb
  else
    cb new Error "No output stream available" if cb

writeHeader = (cb)->
  if 'string' is typeof options.profile
    p = options.profile.split ','
  if Array.isArray options.profile then p = options.profile
  if options.profile is true then p = Object.keys _all_profilers
  for pp in p
    profilers.push _all_profilers[pp]
  for pf in profilers # put to results
    pf.title()
  
  if isJsonFormat
    # Store header keys for JSON format
    headerKeys = results.slice()
    results = []
    cb()
  else
    # Traditional format
    for s in results
      titleL.push s.length # get all length
    o = results.join(' ') + '\n'
    writeOutput o, cb
    title = o
    results = []

rewriteHeader = ->
  if not isJsonFormat and title?
    writeOutput title

writeLine = (cb)->
  if not stream? and not isConsoleOutput
    cb new Error "Not started yet" if cb
    return
  
  for pf,i in profilers
    pf.profile()
  
  if isJsonFormat
    # Create JSON object with header keys and data values
    jsonData = {level:'info',module:'profiler'}
    for key, i in headerKeys
      jsonData[key] = results[i]
    o = JSON.stringify(jsonData) + '\n'
    results = []
    writeOutput o, cb
  else
    # Traditional format
    for s,i in results
      results[i] = fix titleL[i],s
    o = results.join(' ') + '\n'
    results = []
    writeOutput o, cb
    if ++counter >= 100
      rewriteHeader()
      counter = 0

finishProfile = (cb)->
  writeLine()
  if not isConsoleOutput and stream?
    stream.end()
  stream = null
  if cb
    helpers.nextTask cb

module.exports.start = (opt,coffeemate,cb)->
  mate = coffeemate
  if opt?
    options.file = opt.file if opt.file?
    options.freq = opt.freq if opt.freq? and opt.freq > 1000
    options.profile = opt.profile if opt.profile
    options.console = opt.console if opt.console?
    options.formatName = opt.formatName if opt.formatName?
  
  # Set output mode flags
  isConsoleOutput = options.console or false
  isJsonFormat = options.formatName is 'json'
  
  # Only create file stream if not using console output
  if not isConsoleOutput
    stream = fs.createWriteStream options.file,{ flags: 'a'}
  
  writeHeader ->
    writeLine ->
      setInterval writeLine,options.freq
      mate.regExit finishProfile
      if cb then cb()

module.exports.end = ->
  finishProfile()
