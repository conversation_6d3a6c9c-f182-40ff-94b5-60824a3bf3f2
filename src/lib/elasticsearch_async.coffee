

###
Usage:
  INCLUDE lib.elasticsearch
IMPORTANT!
to make _config work, must use INCLUDE but not require to import this lib
###
client = null
verbose = 0
{ Client } = require('@elastic/elasticsearch')
util = require('util')
fs = require 'fs'

module.exports._config_sections = ['elastic']
module.exports._config = _config = (cfg)->
  # (cfg.useSearchEngine is 'ES') and#should be able to impot ES while using mongo
  if not cfg.elastic?.host
    throw new Error('No elastic search config')
  verbose = cfg.elastic?.verbose
  # connection error handle, test connection success.
  # client = new es.Client({
  #   host: cfg.elastic.host,
  #   # log: 'trace'
  # })
  # node: 'http://localhost:9200'
  # if (cfg.useSearchEngine is not 'ES') and (not cfg.elastic?.user)
  #   return
  esConfig = {
    node: cfg.elastic.host,
    auth: {
      username: cfg.elastic.user,
      password: cfg.elastic.password,
    }
  }
  if cfg.elastic?.cert
    esConfig.tls = {
      ca: fs.readFileSync(cfg.elastic.cert),
      rejectUnauthorized: false
    }
  client = new Client(esConfig)

libDebug = require './debug'
debug = libDebug.getDebugger()

exports.ping = ()->
  await client.ping()

exports.getAliases = ()->
  await client.cat.aliases({})

exports.getIndices = ()->
  body = {
    h:['health','status','index','pri','rep','docs.count','docs.deleted','store.size','pri.store.size'],
    format: 'JSON'
  }
  await client.cat.indices(body)

# TODO: use await
exports.getIndexDocCount = ({index})->
  await client.cat.count({index,h:'count'})

exports.putMapping = ({index,body})->
  await client.indices.putMapping({index,body})

# 修改index settings属性
exports.putSetting = ({index,body})->
  await client.indices.putSettings({index,body})

exports.createIndex = ({index, body})->
  exists = await client.indices.exists({index})
  unless exists
    await client.indices.create({index,body})

exports.getIndicesByAlias = ({alias})->
  await client.cat.aliases({format:'json',name:alias})

exports.removeIndex = removeIndex = ({index})->
  await client.indices.delete({index})

exports.createAlias = ({index,alias})->
  await client.indices.putAlias({index,name:alias})

exports.removeAlias = ({index,name})->
  await client.indices.deleteAlias({index,name})

exports.checkIndexExist = ({index}) ->
  await client.indices.exists({index})

_getProfileObj = ()->
  return {
    n:0
    tsMax: 0
    ts: 0
    en:0
  }
gESProfileObj = _getProfileObj()
gESAccessLogAry = []
gLastAccessObj =  _getProfileObj()

getESProfile = ->
  ret = gESProfileObj
  gESProfileObj = _getProfileObj()
  ret

resetAccessObj = ->
  gLastAccessObj = _getProfileObj()

recordESQueryTs = ({ts,body,e})->
  gLastAccessObj.n++
  gLastAccessObj.ts += ts
  if ts > gLastAccessObj.tsMax
    gLastAccessObj.tsMax = ts
  if ts > 10000
    console.error "***Slow Elastic Search Query[#{new Date()}:#{ts/1000}s]: #{JSON.stringify(body)}"
  if e
    gLastAccessObj.en++
    gLastAccessObj.e = e

resetAccessObj()

updateLog = ->
  gLastAccessObj.tsAvg = gLastAccessObj.ts / (gLastAccessObj.n or 1)
  if gLastAccessObj.tsAvg > 2000 # db access average time more than 2s
  #if accessLogAry.length > 1 # test
    # restart server
    console.error "ESDB Access Time Too Long #{gLastAccessObj.tsAvg}ms."
  gESAccessLogAry.push gLastAccessObj
  if gESAccessLogAry.length > 10
    gESAccessLogAry.shift()
  gESProfileObj.n += gLastAccessObj.n
  gESProfileObj.en += gLastAccessObj.en
  gESProfileObj.ts += gLastAccessObj.ts
  if gLastAccessObj.tsMax > gESProfileObj.tsMax
    gESProfileObj.tsMax = gLastAccessObj.tsMax
  resetAccessObj()
setInterval updateLog, 60000

getLogArray = ()->
  return gESAccessLogAry

exports.ESProfile = getESProfile
exports.getLogArray = getLogArray

exports.countDocuments = ({index,query})->
  if verbose > 1
    debug.debug 'ES:Count:',index, '=> ', util.inspect(query, {depth:10}) # JSON.stringify(body) #
  await client.count({index,query})

# exports.searchIndexByBody = ({index,body},cb)->
exports.searchIndexByBody = ({index,body})->
  if verbose > 1
    debug.debug 'ES:Search:',index, '=> ', util.inspect(body, {depth:100}) # JSON.stringify(body) #
  searchStartTime = new Date()
  try
    ret = await client.search({index,body})
    debug.debug 'search ret',ret
    if ret
      took = ret.took
      recordESQueryTs({ts:took,body,e:err})
    # return cb(null,ret)
    return ret
  catch err
    ts = Date.now() - searchStartTime
    recordESQueryTs({ts,body,e:err})
    # cb(err)
    throw(err)

exports.upsertDocument = ({index,id,body})->
  await client.index({index,id,body})

# es批量写入数据
exports.bulkInsertDocuments = ({index,body})->
  operations = body.flatMap (prop) ->
    bulkIndex = { index: { _index: index ,_id:prop._id.toString()} }
    delete prop._id
    [bulkIndex, prop]
  await client.bulk({ operations })

###
# es批量处理数据insert,update,delete
# herf:https://www.elastic.co/guide/en/elasticsearch/reference/current/docs-bulk.html
# Allows to perform multiple index/update/delete operations in a single request.
# index : string
# type body: [{
    type :  string  [index,delete,update] # insert在elastic中为index
    _id : stirng
    prop? : object # delete时没有该信息
}]
###
exports.bulkOperation = ({index,body})->
  operations = body.flatMap (item) ->
    bulkIndex = {}
    bulkIndex[item.type] = {_index: index ,_id:item._id.toString()}
    if item.type is 'delete'
      # delete不需要body信息
      return [bulkIndex]
    else if item.type is 'update'
      # update {doc:{field:value}}
      return [bulkIndex,{doc:item.prop}]
    else
      # insert需要body信息
      return [bulkIndex, item.prop]
  await client.bulk({ operations })

exports.deleteDocById = ({index,id})->
  await client.delete({index,id})

#function may has bug, not able to delete es prop in watch
#if use in the future need to test
# exports.getDocById = ({index,id},cb)->
#   try
#     doc = await client.get({index,id,type:'_doc'})
#     cb null,doc
#   catch err
#     cb err

###
# 更新存储在 Elasticsearch 中的脚本
# @description 将 search_by_wildcard 脚本更新到 Elasticsearch 服务器
# @return {Object} 更新操作的结果
###
exports.updateStoredScript = ()->
  scriptId = 'search_by_wildcard'
  
  # 脚本内容，用于通配符搜索和评分
  scriptSource = """
  def field = params.field;
  def val = doc.containsKey(field) && doc[field].size() > 0 ? doc[field].value.toLowerCase() : "";
  def score = 0;
  def ptype2 = doc.containsKey('ptype2') && !doc['ptype2'].empty ? doc['ptype2'].value : '';
  def isLocker = ptype2 =~ /Locker/i;
  if (isLocker) {
    score -= 25;
  }
  def isParking = ptype2 =~ /Parking/i;
  if (isParking) {
    score -= 30;
  }
  def origScore = doc.containsKey('score') && !doc['score'].empty ? doc['score'].value : 0;
  if (field == "searchAddr") {
    def prefix = params.prefix.toLowerCase();
    def prefixIndex = val.indexOf(prefix);
    if (val.startsWith(prefix)) {
      score += 100;
    } else {
      score += 80;
      if(prefixIndex > 1) {
        def charBeforePrefix = val.charAt(prefixIndex - 1);
        if (charBeforePrefix == ' ') {
          def allWordCharBeforeSpace = true;
          for (int i = 0; i < prefixIndex - 1; i++) {
            def c = val.charAt(i);
            if (!Character.isLetterOrDigit(c) && c != '_') {
              allWordCharBeforeSpace = false;
              break;
            }
          }
          if (allWordCharBeforeSpace) {
            score += 10;
          }
        }
      }
    }
  } else {
    score += 100 - val.length();
  }
  return (500 * score) + origScore;
  """

  # 构造脚本体
  scriptBody = {
    script: {
      lang: 'painless'
      source: scriptSource
    }
  }

  # 使用 client.putScript 方法更新存储脚本
  return await client.putScript({
    id: scriptId
    body: scriptBody
  })
  