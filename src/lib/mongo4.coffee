###
Mongodb driver for RM Models
supports > mongodb 4.10.0
for mongodb <= 3.7.0 use mongo_db.coffee
###
Promise = require 'promise'
util = require 'util'
fs = require 'fs'
mongodb = require 'mongodb'
ObjectID = mongodb.ObjectId or mongodb.ObjectID
MongoClient = require('mongodb').MongoClient
helpersString = require './helpers_string'
helpersFunction = require './helpers_function'
DModule = require './dmodule'
RESOURCE_NAME = '__mongo_db_resource'
# puts = console.log
verbose = 0
servers = {}
dbClients = {}
all_ready = false
expected_connections = 0
alertHandler = null
bServiceStarted = false
debugHelper = require './debug'
debug = debugHelper.getDebugger()

MAX_DB_ACCESS_TIME = 6000

FIND_METHODS = 'find|findOne'.split('|')
# these ops need ret.result.n
NEED_RESULT_FIELD = 'insertOne|insertMany|insert|updateOne|\
updateMany|deleteOne|deleteMany'.split('|')
# these ops modify signle record
ONE_METHODS = 'insertOne|updateOne|findOne|deleteOne|findOneAndUpdate'.split('|')

INSERT_METHODS = 'insertOne|insert|insertAll|insertMany'.split('|')
UPDATE_METHODS = 'update|updateOne|updateMany|findOneAndUpdate'.split('|')
REPLACE_METHODS = 'replaceOne|findOneAndReplace'.split('|')
MONGO_SUPPORTED_METHODS = 'insertOne|insertAll|insertMany|\
replaceOne|updateOne|updateMany|bulkWrite|\
use|distinct|countDocuments|estimatedDocumentCount|createIndex|createIndexes|dropIndex|\
drop|deleteOne|deleteMany|\
findOneAndReplace|findOneAndUpdate|findOneAndDelete|\
find|findOne|sort|toArray|aggregate|group|watch|rename|bulkWrite|stats'
# TODO: ensureIndex
# MongoError: login.save TypeError: Cannot read property 'apply' of undefined
#     at /Users/<USER>/Documents/RealMaster/realmaster-appweb/src/lib/mongo4.coffee:634:39
#     at tryCallTwo (/Users/<USER>/Documents/RealMaster/realmaster-appweb/src/node_modules/promise/lib/

# NOTE: save is removed in mongodb 4.11.0, see https://mongodb.github.io/node-mongodb-native/4.11/modules.html
DEPRECATED_METHODS = 'update|remove|count|insert|save|findAndModify'

PRE_COLLECTION_MAP = {} # 预定义collections

_restarting = false
restartSevice = (e)->
  console.error e
  if alertHandler
    setTimeout (-> alertHandler e,true),0 # raise alert in seperate thread or later
    # only restart when alertHandler is ready
    if _restarting
      console.error 'Already In Restarting'
      return
    _restarting = true
  console.error 'DB:Restarting'

accessLogAry = []
lastAccessObj = null
resetAccessObj = ->
  lastAccessObj =
    n:0
    tsMax: 0
    ts: 0
    en:0
resetAccessObj()
# interface profileObj = {
#   n:'number', #number of db reqs processed
#   tsMax:'number', #max ts used for single req
#   ts:'number', #total ts
#   en:'number', #error numbers(total)
# }
profileObj =
  n:0
  tsMax: 0
  ts: 0
  en:0
getProfileObjAndReset = ->
  ret = profileObj
  profileObj =
    n:0
    tsMax: 0
    ts: 0
    en:0
  ret
updateLog = ->
  lastAccessObj.tsAvg = lastAccessObj.ts / (lastAccessObj.n or 1)
  # db access average time more than 2s
  if bServiceStarted and (lastAccessObj.tsAvg > MAX_DB_ACCESS_TIME)
    #if accessLogAry.length > 1 # test
    # restart server
    restartSevice "DB Access Time Too Long #{lastAccessObj.tsAvg}ms. Restart."
  accessLogAry.push lastAccessObj
  if accessLogAry.length > 10
    accessLogAry.shift()
  profileObj.n += lastAccessObj.n
  profileObj.en += lastAccessObj.en
  profileObj.ts += lastAccessObj.ts
  if lastAccessObj.tsMax > profileObj.tsMax
    profileObj.tsMax = lastAccessObj.tsMax
  resetAccessObj()
setInterval updateLog, 60000


appendParameter = (conn,val='')->
  if conn.indexOf('?') < 0
    return conn+'?'+val
  return conn+'&'+val

# TODO: use opt.connectionString
getConnectStringAndOpt = (key='',opt={})->
  # conn = ""
  srvOpt = {}
  conn = opt.host+':'+opt.port+'/'+opt.name
  if opt.replSet?
    repl = [opt.host+':'+opt.port]
    for rsKey,rs of opt.replSet
      repl.push ((rs?.host or opt.host or '127.0.0.1')+':'+(rs?.port or opt.port or 27017))
    conn = repl.join(',')+'/'+opt.name
    if opt.rsName
      conn = appendParameter conn,('replicaSet='+opt.rsName)
  if opt.user
    conn = 'mongodb://'+encodeURIComponent(opt.user)+\
      ':'+encodeURIComponent(opt.password)+'@'+conn
    if opt.authdb
      # all need authSource now
      # unless opts.dbs.v36 # 3.6 do not allow authSource
      conn = appendParameter conn, "authSource=#{opt.authdb}"
    else
      debug.debug 'no opt.authdb: authSource'
      conn = appendParameter conn, 'authSource=admin'
  else
    conn = 'mongodb://'+conn
  # TODO: load ssl Files by async means
  if opt?.tls
    conn = appendParameter conn,'tls=true'
    srvOpt = {tls:opt.tls}
    # https://www.mongodb.com/docs/drivers/node/current/fundamentals/connection/tls/
    for oKey in ['tlsCAFile','tlsCertificateKeyFile','tlsCertificateKeyFilePassword',\
    'tlsAllowInvalidHostnames','tlsAllowInvalidCertificates']
      if (oVal = opt[oKey])?
        srvOpt[oKey] = oVal
  else if opt?.ssl
    conn = appendParameter conn,'ssl=true'
    srvOpt = {ssl:opt.ssl,sslValidate:opt.sslValidate}
    # http://mongodb.github.io/node-mongodb-native/2.0/api/Server.html
    for oKey in ['sslCA','sslCert','sslKey']
      if oVal = opt[oKey]
        srvOpt[oKey] = fs.readFileSync oVal
  else
    srvOpt = {}
  srvOpt.maxPoolSize = opt?.poolSize or 20
  delete srvOpt.poolSize
  if opt?.socketOptions
    srvOpt = Object.assign srvOpt, opt.socketOptions
    delete srvOpt.autoReconnect
  connectTimeoutMS = opt?.socketOptions?.connectTimeoutMS or 300000
  socketTimeoutMS = opt?.socketOptions?.socketTimeoutMS or 0
  conn = appendParameter conn,"connectTimeoutMS=#{connectTimeoutMS}&socketTimeoutMS=#{socketTimeoutMS}"
  # srvOpt.version = opt?.version or 3.6
  # if opt?.authdb
  #   srvOpt.authSource = opt.authdb
  if opt.readPrimary
    debug.debug "Read Primary #{key}"
    srvOpt.readPreference = mongodb.ReadPreference.PRIMARY
    conn = appendParameter conn,"readPreference=primary"
  else
    srvOpt.readPreference = mongodb.ReadPreference.NEAREST
    conn = appendParameter conn,"readPreference=nearest"
    if opt.tag2ReadFrom
      readTags = []
      for k,v of opt.tag2ReadFrom
        readTags.push "#{k}:#{v}"
      if readTags.length
        conn = appendParameter conn,"readPreferenceTags=#{readTags.join(',')}"
  sname = key
  return {conn,srvOpt,sname}

storeServerAndClients = (sname,dbClient,opt,srvOpt)->
  if dbClient.db
    servers[sname] = dbClient.db opt.name # mongodb3.0 use client.db
    dbClients[sname] = dbClient
  else
    servers[sname] = dbClient
  if opt.isDefault
    servers['_default'] = servers[sname]
    console.log '*Default DB:' + sname
  dbClient.version = srvOpt.version
  return null

MongoDb =
  setMaxDbAccessTime: (ts)->
    MAX_DB_ACCESS_TIME = ts
  setup: (opts, cb)->
    if opts?.dbs?
      for key,opt of opts.dbs
        if opt.rsName
          rsName = opt.rsName
    if opts?.dbs?.oldDb
      # @setup_old opts,cb
      return throw new Error('oldDb not supported, use mongo_db.coffee instead')
    else
      @setup_new opts,cb
  setAlertHandler: (handler)->
    alertHandler = handler
  setReady: ()->
    resetAccessObj()
    bServiceStarted = true
  setup_new: (opts,cb) ->
    verbose = opts?.verbose? or verbose
    DModule.depend RESOURCE_NAME,(err,handler)->
      if cb? then cb err,handler
      null
    if not opts?.dbs?
      return debug.debug 'No mongodb to setup in config'
    n = 0
    if opts.dbs.verbose? then verbose = opts.dbs.verbose
    if verbose > 2 then console.log opts.dbs
    for key,opt of opts.dbs
      continue if 'object' isnt typeof opt
      # Check if opt contains a URI
      if opt.uri?
        conn = opt.uri
      else
        {conn, srvOpt, sname} = getConnectStringAndOpt(key, opt)
      # new config will use default uri for rni, so use rni='' when no rni connection
      continue if conn is ''
      expected_connections++
      # srvOpt = { useNewUrlParser: true, useUnifiedTopology: true }
      srvOpt = {} # default useNewUrlParser and useUnifiedTopology in mongodb driver v6.x
      sname = key
      # TODO: use let if ES6 js, or forEach
      do (sname, conn, srvOpt, opt)->
        process.nextTick ->
          if verbose then console.log "Mongo Openning #{sname}"
          console.log conn
          console.log srvOpt
          try
            dbClient = await MongoClient.connect conn, srvOpt
            expected_connections--
            storeServerAndClients(sname,dbClient,opt,srvOpt)
            # BUG: do not directly accesss private properties, may change in future versions, should access public methods
            # dbName = dbClient.db()?.s?.namespace?.db
            # NOTE: refer to https://mongodb.github.io/node-mongodb-native/5.9/classes/Db.html#namespace  https://github.com/mongodb/node-mongodb-native/blob/v5.9.0/src/db.ts#L214
            dbName = dbClient.db().databaseName or dbClient.db().namespace
            if verbose then console.log "Mongo Ready: #{sname} => #{dbName}"
            check_ready null
          catch err
            console.error "Mongo DB open error #{err}"
            throw err unless opt.ignoreError
  get_db: (sname,cb)->
    if cb
      throw new Error("cb not supported for get_db, sname:#{sname}")
    if not sname?
      sname = '_default'
    db = servers[sname]
    if db
      return db
    throw new Error("DB not found #{sname}")
    return null
  get_db_client: (sname)->
    if not sname?
      sname = '_default'
    dbClients[sname]
  ObjectID: ObjectID
  ObjectId: ObjectID
  set_predef_colls: (opts,cb)->
    if not opts?
      return cb()
    keys = Object.keys(opts)
    doOne = ->
      unless key = keys.shift()
        return cb()
      opt = opts[key]
      if opt.dbName? and opt.collName?
        db = new Database(opt.dbName,opt.collName)
        db.get_coll opt.dbName,opt.collName,opt.options,(err,coll)->
          if err then return cb(err)
          PRE_COLLECTION_MAP[key] = db
          return doOne()
      else
        return cb("No dbName or collName, options:#{JSON.stringify opt}")
    doOne()

setup_default_db = ->
  if not servers['_default']
    key = Object.keys servers
    servers['_default'] = servers[key[0]]
    console.log "Default DB:#{key[0]}"

check_ready = (err)->
  if all_ready or all_ready is 0 then return null
  if err
    console.log "MongoDB Setup Error #{err}"
    DModule.provide RESOURCE_NAME,err
    all_ready = 0 # never ready
  if expected_connections <= 0
    all_ready = true
    if verbose then console.log 'MongoDB Setup Done'
    setup_default_db()
    DModule.provide RESOURCE_NAME,null,MongoDb
  null

setup_server = (sname,opt)->
  expected_connections++
  s = new Server((opt.host or '127.0.0.1'),(opt.port or 27017),{})
  servers[sname] = new mongodb.DB(opt.name, s)

#  > 2params, {tsStart,err,cursor}
recordTsDiff = ({tsStart,err,cursor,collname,method,args})->
  tsDiff = Date.now() - tsStart
  lastAccessObj.n++
  lastAccessObj.ts += tsDiff
  if tsDiff > lastAccessObj.tsMax
    lastAccessObj.tsMax = tsDiff
  if tsDiff > 10000
    console.error "***Slow Query[#{new Date()}:#{tsDiff/1000}s]\
    [#{collname}.#{method}]: #{util.inspect(args[0])} \
    #{if 'function' isnt typeof args[1] then util.inspect(args[1]) else 'function'}"
    if curInfo = cursor?.server?.s
      console.error '***Cursor','description:',curInfo.description,\
        'options:',curInfo.options,'pool:',curInfo.pool
  if err
    lastAccessObj.en++
    lastAccessObj.e = err
    if err.code is 96
      console.error 'DB Operation Failed.',collname,args

# @return
# interface options {}
getDefaultTransactionOptions = ()->
  return {
    readPreference: 'primary'
    readConcern: { level: 'local' }
    writeConcern: { w: 'majority' }
  }

class Database
  constructor: (@dbname,@collname)->
    @ObjectID = ObjectID
    @ObjectId = ObjectID
    @ReadPreferencePrimary = new mongodb.ReadPreference(mongodb.ReadPreference.PRIMARY)
    @ReadPreferenceNearest = new mongodb.ReadPreference(mongodb.ReadPreference.NEAREST)
  db: (@dbname)-> @
  coll: (@collname)-> @
  toString: -> if @collname then "[#{@dbname}:#{@collname}]" else "[#{@dbname}]"
  admin: ()-> MongoDb.get_db().admin()
  getlogArray: -> accessLogAry
  newDatabase: (db,coll)-> new Database(db,coll)
  get_db: (dbname)-> return MongoDb.get_db dbname
  ###
  # @description  获取collection,如果不存在则创建并返回collection
  # @params dbName - database name
  # @params collName - collection name
  # @params options - options of create collection,eg:{expireAfterSeconds:'',size:'',max:''}
  #           REF:https://www.mongodb.com/docs/manual/reference/method/db.createCollection
  # @params callback - callback function
  # @return collection
  ###
  get_coll: (dbName,collName, options, callback)->
    if not callback?
      callback = options
      options = collName
      collName = dbName
      dbName = @dbname
    if not callback?
      callback = options
      options = collName
      collName = @collname
    if not callback?
      callback = options
    if (not callback?) or ('function' isnt typeof callback)
      return callback('No callback or callback isnt function')
    options = options or {}
    try
      db = MongoDb.get_db dbName
      console.log 'get_db in -->',dbName,'get_coll'
      # 可能存在时序表的情况,需要先判断是否需要创建collection
      isExist = await db.listCollections({ name: collName }).toArray()
      if isExist?.length
        coll = await db.collection collName
      else
        coll = await db.createCollection collName,options
    catch error
      return callback error
    return callback null,coll

  # get_coll: (dbname,collname,callback)->
  #   if not callback?
  #     callback = collname
  #     collname = dbname
  #     dbname = @dbname
  #   if not callback?
  #     callback = collname
  #     collname = @collname
  #   console.log "MSG: get_coll DB:#{dbname}\tColl:#{collname}"
  #   try
  #     db = MongoDb.get_db dbname
  #     console.log 'get_db in -->','get_coll'
  #     coll = await db.collection collname
  #     callback coll
  #   catch
  #     if err then return callback err
  isObjectIDString: (fid)->
    return helpersString.isObjectIDString fid
  # convert to ObjectID, if the string is in ObjectID format
  toObjectID: (id)->
    return new ObjectID(id) if @isObjectIDString id
    id
  # args need to be modifyed according to method
  modifyArgObjectMtByMethod:(method,args)->
    shouldUpdateMt = true
    if (args[1]?.noModifyMt or args[2]?.noModifyMt)
      shouldUpdateMt = false
      # self defined value, must delete, otherwise cause MongoInvalidArgumentError: Update document requires atomic operators
    delete args[1].noModifyMt if args[1]
    delete args[2].noModifyMt if args[2]
    return if not shouldUpdateMt
    if method in INSERT_METHODS
      if Array.isArray args[0] #
        objAry = args[0].map (obj)->
          Object.assign({},obj,{_mt:new Date()}) # shall not change original object and array
        args[0] = objAry
      else
        obj = Object.assign({},args[0],{_mt:new Date()}) # Shall not change original object
        args[0] = obj
    else if method in UPDATE_METHODS
      if Array.isArray args[1] # check set in array
        for obj in args[1]
          if obj.$set
            # shall not change original object and array
            obj.$set = Object.assign({},obj.$set,_mt:new Date())
            setMt = true
        if not setMt
          args[1].push {$set:{_mt:new Date()}}
      else
        if args[1].$set?
          # shall not change original object and array
          args[1].$set = Object.assign({},args[1].$set,_mt:new Date())
        else
          args[1].$set = {_mt:new Date()}
    else if method in REPLACE_METHODS
      # shall not change original object and array
      args[1] = Object.assign({},args[1],_mt:new Date())
    else if method in FIND_METHODS
      # NOTE: fields -> projection
      if args[1]?.fields
        if Array.isArray(args[1].fields)
          args[1].projection = {}
          for value in args[1].fields
            args[1].projection[value] = 1
        else
          args[1].projection = Object.assign({},args[1].fields)
        delete args[1].fields
      # args[1] = Object.assign({},args[1],_mt:new Date())
  q_normalize_id: (f)->
    if f
      f = f.$query if f.$query
      if fid = f._id
        if 'object' is typeof fid and fid.$in
          fid.$in = (@toObjectID(id) for id in fid.$in)
        else
          f._id = @toObjectID fid
      f.uid = @toObjectID(f.uid) if f.uid
    f
  execute: (method,args)->
    self = @
    tsStart = Date.now()
    cursor = null
    return new Promise((resolve,reject)->
      if ('function' is typeof args[args.length - 1])
        err = new Error("not support #{method} with cb")
        reject err
      try
        db = self.get_db self.dbname
        # console.log 'get_db in -->','execute'
        coll = db.collection self.collname
        if not (args[1]?.noQueryNormalize or args[2]?.noQueryNormalize)
          self.q_normalize_id args[0]
        if verbose > 2
          strOpt = ''
          if args[1]?
            if args[1].session
              strOpt = util.inspect(Object.assign({},args[1],{session:args[1].session.toString()}))
            else
              strOpt = util.inspect(args[1])
          debug.debug "DB: #{self.dbname or 'DefaultDB'}.#{self.collname}:\
          #{method} => #{util.inspect(args[0],{depth:5})}, \
          #{strOpt}"
        self.modifyArgObjectMtByMethod(method,args)
        # if verbose > 2
        #   puts "DB: #{self.dbname} #{self.collname} #{method}: #{util.inspect args}"
        # https://www.mongodb.com/docs/drivers/node/current/usage-examples/changeStream/
        if (['watch'].indexOf(method) >= 0)
          ret = coll[method].apply coll,args
        # https://www.mongodb.com/docs/drivers/node/current/fundamentals/crud/read-operations/cursor/
        else if (['find'].indexOf(method) >= 0) \
        and (args.length > 0) and \
          # ret==cursor
          ret = await coll[method].apply coll,args
          # console.log '++++execure',ret
        # aggregate need to call toArray
        else if method is 'aggregate' and ((not db.version?) or (db.version >= 3.6))
          cursor = coll[method].apply coll,args
          ret = await cursor.toArray()
        else if method is 'stats' # for mongodb 6.0
          ret = await db.command({ collStats: coll.collectionName })
        else if method in ['findOneAndUpdate','findOneAndReplace'] # for mongodb driver v6.X
          # Initialize args[2] if it doesn't exist
          args[2] ?= {}
          args[2].includeResultMetadata ?= true
          ret = await coll[method].apply coll,args
        # findOneAndDelete函数传参只有query,与findOneAndUpdate,findOneAndReplace不同
        else if method is 'findOneAndDelete' # for mongodb driver v6.X
          # Initialize args[1] if it doesn't exist
          args[1] ?= {}
          args[1].includeResultMetadata ?= true
          ret = await coll[method].apply coll,args
        else
          ret = await coll[method].apply coll,args
          # console.log '++++++execute',method,ret if /delete/ig.test method
          if method in NEED_RESULT_FIELD
            ret ?= {}
            ret.result ?= {}
            ret.result.ok ?= if ret.acknowledged then 1 else 0
            if /delete/ig.test method
              ret.result.n ?= ret.deletedCount
            else if method in ONE_METHODS
              ret.result.n ?= if ret.matchedCount? then ret.matchedCount else 1
              ret.result.nModified ?= if ret.modifiedCount? then ret.modifiedCount else 1
            else
              ret.result.n ?= ret.insertedCount or ret.insertedIds?.length or ret.matchedCount
              ret.result.nModified ?= if ret.modifiedCount? then ret.modifiedCount else ret.result.n
        # console.log '++++++execute',method,ret
        recordTsDiff({
          tsStart,err:null,cursor,
          collname:self.collname,method,
          args:args
          })
        resolve ret
      catch err
        console.error 'MongoError:',self.collname+'.'+method,err,args
        # NOTE: Cross db transaction not support for mongodb 6.0
        if /Given transaction number \d+ does not match any in-progress transactions|No keys found for HMAC that is valid/ig.test err
          console.error 'TIP: Cross db transaction not support for mongodb, need to be under same db for transaction!'
        recordTsDiff({
          tsStart,err,cursor,
          collname:self.collname,method,
          args:args})
        reject err
        # throw err
    )
  # query [,fields [,sort [,limit [,skip]]]],callback
  # interface arguments {
  #   0: object #query object
  #   1: object #query options
  # }
  findToArrayWithoutCb: ()->
    self = @
    tsStart = Date.now()
    args = arguments
    query = null
    cursor = null
    # NOTE: if you're in any other asynchronous callback, you must use reject. https://stackoverflow.com/questions/33445415/javascript-promises-reject-vs-throw
    return new Promise((resolve,reject)->
      if 'function' is typeof args[args.length-1]
        err = new Error('Last parameter for \
        findToArrayWithoutCb must not be a function(err,data)')
        return reject err
      try
        db = self.get_db self.dbname
        # console.log 'get_db in -->','findToArray'
        coll = db.collection self.collname
        #console.dir args
        if args.length is 1 and 'object' isnt typeof args[0]
          return reject(new Error('Bad Request',null))
        args[0] = self.q_normalize_id(args[0]) if not args[1]?.noQueryNormalize
        # if verbose then puts "find: #{util.inspect args}"
        if args[1]?.fields
          args[1].projection = Object.assign({},args[1].fields)
          delete args[1].fields
        if verbose > 2
          strOpt = ''
          if args[1]?
            if args[1].session
              strOpt = util.inspect(Object.assign({},args[1],{session:args[1].session.toString()}))
            else
              strOpt = util.inspect(args[1])
          debug.debug "DB: #{self.dbname or 'DefaultDB'}.#{self.collname}:\
          findToArray => #{util.inspect(args[0])}, \
          #{strOpt}"
        query = args[0]
        #if (args.length > 0) and (fields = args.shift()) # when has fields, and fields is not null
        #  res = coll['find'].apply(coll,[query,fields])
        #else
        #  res = coll['find'].call(coll,query)
        #res = coll.find query
        #res = res.sort args.shift() if args.length > 0
        #res = res.limit args.shift() if args.length > 0
        #res = res.skip args.shift() if args.length > 0
        if args[1]? # mongo drive 2.0 take options for skip limit fields
          cursor = coll.find query,args[1]
        else
          cursor = coll.find query
        ret = await cursor.toArray()
        recordTsDiff({
          tsStart,err:null,cursor,
          collname:self.collname,method:'findToArray',
          args:args
          })
        resolve ret
      catch err
        recordTsDiff({
          tsStart,err,cursor,collname:self.collname,
          method:'findToArray',
          args:args
          })
        console.log 'MongoError:',util.inspect(args[0])
        reject err
        # throw err
    )
    

  objectIdWithTimestamp: (timestamp)->
    # Convert string date to Date object (otherwise assume timestamp is a date)
    if 'string' is typeof timestamp
      timestamp = new Date(timestamp)
    # Convert date object to hex seconds since Unix epoch
    hexSeconds = Math.floor(timestamp/1000).toString(16)
    # Create an ObjectId with that hex timestamp
    new ObjectID(hexSeconds + '0000000000000000')

  # NOTE: DO NOT call cb inside try code block
  # TODO: @luoxiaowei unittest for transaction
  # USAGE: as follows
  ###
async function placeOrder(client, cart, payment) {
  {transactionOptions,session} = db.getTransaction()
  try {
    session.startTransaction(transactionOptions);

    const orderResult = await ordersCollection.insertOne {...},{ session }
    const checkInventory = await inventoryCollection.findOne {...},{ session }
    if (checkInventory === null) {
      throw new Error('Insufficient quantity or SKU not found.');
    }
    await inventoryCollection.updateOne {q},{...},{ session }
    await customerCollection.updateOne {q},{...},{ session }
      
    await session.commitTransaction();
    console.log('Transaction successfully committed.');
  } catch (error) {
    if (error instanceof MongoError && error.hasErrorLabel('UnknownTransactionCommitResult')) {
      // add your logic to retry or handle the error
    }
    else if (error instanceof MongoError && error.hasErrorLabel('TransientTransactionError')) {
      // add your logic to retry or handle the error
    } else {
      console.log('An error occured in the transaction, performing a data rollback:' + error);
    }
    await session.abortTransaction();
  } finally {
    await session.endSession();
  }
}
###
  # @input
  # interface opt {
  #   maxCommitTimeMS:number, #override opts for session
  # }
  getTransaction:(opt={})->
    transactionOptions = Object.assign({},getDefaultTransactionOptions(),opt)
    client = MongoDb.get_db_client @dbname
    db = @get_db @dbname
    session = client.startSession()
    return {transactionOptions,session}

  # start a new session for transactions.
  # parameters can be (dbname) or (options) or (dbname,options) or nothing()
  # dbname @param: database name
  # opt @param: session options
  # usage: https://www.mongodb.com/blog/post/quick-start-nodejs--mongodb--how-to-implement-transactions
  startSession: (opt)->
    if not @dbname
      throw new Exception('Need a database name')
    db = @get_db @dbname
    if not db
      throw new Exception('no database of ' + @dbname)
    db.startSession opt
  # usage:
  #  transaction dbname, options, (dbClient, session)->
  #     await dbClient.get_coll('coll').updateOne(xxx,{session})
  #     await dbClient.get_coll('coll2').updateOne(xxx,{session})
  transaction: (transactionOptions, fnTransaction)->
    if (not fnTransaction)
      fnTransaction = transactionOptions
      transactionOptions = getDefaultTransactionOptions()
    try
      client = MongoDb.get_db_client @dbname
      db = @get_db @dbname
      session = client.startSession()
      transactionResults = await session.withTransaction( (()->
        # debug.debug 'start transaction',@dbname
        await fnTransaction db, session
        # debug.debug 'end transaction',@dbname
      ), transactionOptions)
      return transactionResults
      # has for success, null for aborted
      # } catch (e) {
      #   throw e;
    finally
      await session.endSession()
  
#NOTE: deprecations, see deprecatedMethods.md


Database_method_setup = ->
  # setup deprecate methods
  for name in DEPRECATED_METHODS.split '|'
    do (name)->
      Database.prototype[name] = ->
        args = [].slice.call(arguments)
        # invoke without cb
        deprecateErr = new Error("MongoError: #{name} \
          no longer supported, deprecated in 3.6.12 already")
        console.log deprecateErr
        if 'function' is typeof (lastParam = args[args.length-1])
          cb = args.pop()
          return cb deprecateErr
        else
          # return a new promise
          return new Promise((resolve,reject)->
            return reject(deprecateErr)
          )
  for name in MONGO_SUPPORTED_METHODS.split '|'
    do (name)->
      Database.prototype[name] = ->
        args = [].slice.call(arguments)
        # invoke without cb
        if 'function' is typeof (lastParam = args[args.length-1])
          try
            cb = args.pop()
            ret = await @execute name,args
            # cb = async fn()
            return cb null,ret
          catch err
            return cb err
        else
          # return a new promise
          return @execute name,args
          
  Database.prototype.findToArray = ->
    args = [].slice.call(arguments)
    if 'function' is typeof (lastParam = args[args.length-1])
      try
        cb = args.pop()
        ret = await @findToArrayWithoutCb args...
        return cb null,ret
      catch err
        return cb err
    else
      # return a promise
      return @findToArrayWithoutCb args...
  Database.prototype.ensureIndex = (fields,opt,cb)->
    if (not cb?) and ('function' is typeof opt)
      cb = opt
      opt = null
    self = @
    try
      db = @get_db self.dbname
      console.log 'get_db in -->','ensureIndex'
      coll = db.collection self.collname
      if opt?
        result = await coll.createIndex fields,opt
      else
        result = await coll.createIndex fields
      cb(null,result) if cb
    catch err
      console.error err
      console.error "ensureIndex error for #{self.collname}"
      console.error fields
      console.error opt
      cb err if cb
  # watchTarget a collection for change
  # usage:
  # 1. default options, watch all change and deal with error automatically.
  #    db.watchTarget function(fullDoc,cb)
  # 2. customized options.
  #    db.watchTarget options,cb as in watchHelper.watchTarget
  Database.prototype.watchTarget = (opt,cb)->
    # TODO:

  null

Database_method_setup()

module.exports = MongoDb
module.exports.Database = Database
module.exports.profile = getProfileObjAndReset

###
predefine collection 为了解决不同环境下的问题，
eg. 主程序sendmail记录到chome，batchEdm的sendmail记录到rni

# @description get/create collection
# @param {string} key - predefine collection key
# @return class instance - Database instance
###
module.exports.getPreDefinedColl = getPreDefinedColl = (key)->
  return PRE_COLLECTION_MAP[key]
    
module.exports.DEPRECATED_METHODS = DEPRECATED_METHODS    