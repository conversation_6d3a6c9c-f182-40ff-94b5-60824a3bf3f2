###
# TODO: more documentaion

DEBUG module
6 level of information:
  -2 critical:
  -1 error:
  0  warn:
  1  info: >> logs/info_log
  2  verbose:
  3  debug: >> logs/info_log

find (err,obj)->
  if err
    DEBUG.error ['SYS','DB'],err
    # console.error no  need, 'Error Msg ' no need
    return cb MSG_STRINGS.DB_ERR if err


TODO:
  - predefined category
  - support category/tags
  - support remote debug
  - support dynamic configuration: level, file, category/tags
  - support color, use https://github.com/chalk/chalk
###

#https://stackoverflow.com/questions/14172455/get-name-and-line-of-calling-function-in-node-js/14172822
#get line number when error and warning

util = require 'util'

gErrorOutputHandler = console.error

gOutputHandler = console.log

gFormInputOutputHandler = console.log

# Global output format setting - 'text' or 'json'
gOutputFormat = 'text'

module.exports.setErrorOutputHandler = (handler)->
  gErrorOutputHandler = handler

module.exports.setOutputHandler = (handler)->
  gOutputHandler = handler

module.exports.setFormInputOutputHandler = (handler)->
  gFormInputOutputHandler = handler

module.exports.setOutputFormatName = (format)->
  if format in ['text', 'json']
    gOutputFormat = format
  else
    throw new Error "Unsupported output format: #{format}. Supported formats: text, json"

pathArr = process.argv[1]?.split('/')
batchNm = pathArr[pathArr.length-1] if pathArr
DEFAULT_MODULE = batchNm or 'SYSTEM'

###
# add this line to start.sh to set NO_SUPPRESS_IN_DEBUG
```
export NO_SUPPRESS_IN_DEBUG=true
```
###
gNoSuppressInDebug = process.env.NO_SUPPRESS_IN_DEBUG

module.exports.DEBUG = DEBUG = 3
module.exports.VERBOSE = VERBOSE = 2
module.exports.INFO = INFO = 1
module.exports.WARNING = WARNING = 0
module.exports.ERROR = ERROR = -1
module.exports.CRITICAL = CRITICAL = -2
FORM_INPUT = -99
# TODO: start.sh set process.env.DEBUG if not developer mode
if process.env.DEBUG_THRESHOLD?
  DEFAULT_THRESHHOLD = process.env.DEBUG_THRESHOLD
else
  DEFAULT_THRESHHOLD = if process.env.DEBUG then DEBUG else INFO

levelMapN2S =
  '3': 'DEBUG'
  '2': 'VERBOSE'
  '1': 'INFO'
  '0': 'WARNING'
  '-1': 'ERROR'
  '-2': 'CRITICAL'
  '-99': 'FORM_INPUT'

# 对应等级限制重复n次后打印
gSuppressShowingCountLimits =
  '0' : 20 # WARNING
  '-1' : 5 # ERROR

#重启会清空
gDebugMessages={}
gKeyCount  = 0 #大于 1000清空
KEYCOUNT_RESET_THRESHOLD = 1000

resetSuppressCounter = ()->
  #console.log 'gDebugMessages',gDebugMessages
  gKeyCount  = 0
  gDebugMessages = {}
d2 = (d)-> if d < 10 then '0'+d else d
formatDate = (dt)->
  dt.getFullYear()+'-'+d2(dt.getMonth()+1)+'-'+d2(dt.getDate())+'T'\
  +d2(dt.getHours())+':'+d2(dt.getMinutes())+':'+d2(dt.getSeconds())+'.'+dt.getMilliseconds()

class Debug
  constructor: (file,_module)->
    @file = file
    @module = _module
    @threshhold = Math.min @file.threshhold,@module.threshhold
  dontUserConsoleError: (tf)->
    if tf and (gErrorOutputHandler is console.error)
      gErrorOutputHandler = console.log
  getThreshold: ->
    Math.min @file.threshhold,@module.threshhold
  setThreshold: (newTH)->
    @threshhold = newTH

  _output: (level,msgs) ->
    if gOutputFormat is 'json'
      @_outputJson level, msgs
    else
      @_outputText level, msgs

  _outputJson: (level, msgs) ->
    cleanMsgs = @_cleanMessagesForJson(msgs)
    jsonOutput = {
      level: levelMapN2S[level]
      date: formatDate(new Date())
      module: @module.moduleName
      file: @file.fileName
      msg: cleanMsgs
    }
    outputStr = JSON.stringify(jsonOutput) + '\n'
    @_routeOutput level, outputStr

  _outputText: (level, msgs) ->
    msgs.unshift "#{levelMapN2S[level]}:#{@module.moduleName},\
      #{@file.fileName},#{formatDate(new Date())}\n"
    msgs.push '\n'
    @_routeOutput level, msgs...

  _cleanMessagesForJson: (msgs) ->
    cleanMsgs = []
    if Array.isArray(msgs)
      for msg in msgs
        if 'string' is typeof msg
          # Remove newlines and normalize spaces
          cleanMsg = msg.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim()
          cleanMsgs.push cleanMsg
        else
          cleanMsgs.push msg
    else if 'string' is typeof msgs
      cleanMsgs = msgs.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim()
    else
      cleanMsgs = msgs
    return cleanMsgs

  _routeOutput: (level, output) ->
    if level is FORM_INPUT
      gFormInputOutputHandler output
    else if level < 0
      gErrorOutputHandler output
    else
      gOutputHandler output

  _log: (level,msgs)->
    # console.log '======',level,@file.threshhold,@module.threshhold,msgs
    # console.log '@file',@file,'@module',@module
    #return if (level > @file.threshhold) and (level > @module.threshhold)
    # BUG: use getThreshold
    return if level > @threshhold

    strLevel = '' + level
    # 如果不是在控制类型，直接打印
    if gNoSuppressInDebug or (not gSuppressShowingCountLimits[strLevel]?)
      @_output level,msgs
      return

    # calculate message key
    if Array.isArray(msgs)
      newMsgs = []
      for msg in msgs
        if 'object' is typeof msg
          msg = util.inspect msg,{depth:6}
        #console.log '****',strLevel,msg
        newMsgs.push msg
      msgs = newMsgs
      msgstr = msgs.join(',')
    else if 'object' is typeof msgs
      msgstr = util.inspect msgs # JSON.stringify msgs
      msgs = [msgstr]
      #console.log '@@@@',strLevel,msgs
    else
      msgstr = msgs.toString()
      #console.log '!!!!',strLevel,msgs

    #key长度大于200限制key为前后100字符
    if msgstr.length > 200
      msgKey = "#{msgstr.slice(0,100)}#{msgstr.slice(-100)}"
    msgKey = "#{levelMapN2S[strLevel]} #{msgstr}"

    if gDebugMessages[msgKey]?
      if 0 is (gDebugMessages[msgKey]++ % gSuppressShowingCountLimits[strLevel])
        @_output level,msgs
      return
    gDebugMessages[msgKey] = 1
    gKeyCount++
    @_output level,msgs
    if gKeyCount >= KEYCOUNT_RESET_THRESHOLD
      resetSuppressCounter()
  critical: (args...)->
    args.unshift __line
    args.unshift __function
    args.unshift __fileName
    @_log CRITICAL, args
  error: (args...)->
    args.unshift __line
    args.unshift __function
    args.unshift __fileName
    @_log ERROR, args
  warn: (args...)->
    args.unshift __line
    args.unshift __function
    args.unshift __fileName
    @_log WARNING, args
  info: (args...)->
    @_log INFO, args
  log: (args...)->
    @_log INFO, args
  verbose: (args...)->
    @_log VERBOSE, args
  debug: (args...)->
    @_log DEBUG, args
  formInput: (args...)->
    @_log FORM_INPUT, args

module.exports.Debug = Debug

module.exports.setDefaultModule = (defModule)->
  DEFAULT_MODULE = defModule if defModule

allDebuggers = {}
allFiles = {}
allModules = {}
# watchMongo, watch, 0
###
# Initialize or get an existing debugger
# @param fileName[string] the source file name
# @param moduleName[string] optional, the module name for this debuger
# @param fTH[number] optional, the file level threshholld
# @param mTH[number] optional, the module level threshhold
# @eg.(inside sqlDBS.config) debug = debugHelper.getDebugger __filename,'property'
###

# developer mode, 默认3？
module.exports.getDebugger = getDebugger = (moduleName=DEFAULT_MODULE,\
    fTH=DEFAULT_THRESHHOLD,mTH=DEFAULT_THRESHHOLD)->
  if not ('string' is typeof moduleName)
    mTH = fTH or DEFAULT_THRESHHOLD
    fTH = moduleName or DEFAULT_THRESHHOLD
    moduleName = DEFAULT_MODULE
  fileName = __fileName2
  file = allFiles[fileName] ?= {threshhold:fTH,fileName:fileName.replace(/^.*\/src\//,'')}
  if fTH > file.threshhold
    file.threshhold = fTH
  # NOTE: module name conflicts with global
  _module = allModules[moduleName] ?= {threshhold:mTH,moduleName:moduleName}

  if mTH > _module.threshhold
    _module.threshhold = mTH

  ret = (allDebuggers[moduleName+'.'+fileName] ?= new Debug(file,_module))
  ret

module.exports.setFileThreshold = setFileThreshold = (fileName,newTH)->
  file = allFiles[fileName]
  file.threshhold = newTH

module.exports.setModuleThreshhold =setModuleThreshhold =  (moduleName,newTH)->
  unless newTH
    newTH = moduleName
    moduleName = DEFAULT_MODULE
  _module = allModules[moduleName]
  _module.threshhold = newTH

module.exports.setDefaultThreshhold = (defTH)->
  DEFAULT_THRESHHOLD = defTH

module.exports.increseModuleThreshhold = (moduleName, increase)->
  if not increase
    increase = moduleName
    moduleName = DEFAULT_MODULE
  return if 'number' isnt typeof increase
  return unless _module = allModules[moduleName]
  _module.threshhold += increase
  _module.threshhold = 3 if _module.threshhold > 3
  _module.threshhold = -1 if _module.threshhold < -1
  console.log "change #{moduleName} log level to #{_module.threshhold},#{levelMapN2S[_module.threshhold]}"
  setModuleThreshhold(moduleName, _module.threshhold)

# NOTE: this debug.coffee called twice
Object.defineProperty(global, '__stack', {
  configurable: true,
  # writable: true,
  get: ()->
    orig = Error.prepareStackTrace
    #拦截prepareStackTrace，返回structured stack track。
    Error.prepareStackTrace = (_, stack)->
      return stack
    err = new Error
    Error.captureStackTrace(err, arguments.callee)
    stack = err.stack
    Error.prepareStackTrace = orig
    return stack
})
# TypeError: Cannot redefine property: __line
Object.defineProperty(global, '__line'
  configurable: true,
  get:()->
    return __stack[2].getLineNumber()
)

Object.defineProperty(global, '__function', {
  configurable: true,
  get:()->
    i = 2
    fileName = __stack[i].getFileName()
    while i < __stack.length
      if funName = __stack[i].getFunctionName() or __stack[i].getMethodName()
        return funName
      i++
    return 'noNameFunction'
})

Object.defineProperty(global, '__fileName', {
  configurable: true,
  get:()->
    return __stack[2].getFileName().replace(/^.*\/src\//,'')
})
# TODO: confirm batch stack index
Object.defineProperty(global, '__fileName2', {
  configurable: true,
  get:()->
    return __stack[2].getFileName().replace(/^.*\/src\//,'')
})
